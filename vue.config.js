module.exports = {
    publicPath: './',
    productionSourceMap: false,
    //使用等比适配插件
    lintOnSave: true,
    configureWebpack: {
        devtool: 'source-map',
        externals: {
            AMap: 'AMap',
            vue: 'Vue',
            'vue-router': 'VueRouter',
            vuex: 'Vuex',
            'element-ui': 'ELEMENT',
            jquery: 'jQuery',
            echarts: 'echarts',
            './cptable': 'var cptable'
            // "echarts-gl": "echarts-gl",
        },
        devServer: {
            // overlay: { // 让浏览器 overlay 同时显示警告和错误
            //     warnings: true,
            //     errors: true
            // },
            // host: "localhost",
            port: 8991, // 端口号
            // https: false, // https:{type:Boolean}
            open: true, //配置自动启动浏览器y
            // hotOnly: true, // 热更新
            proxy: {
                'ah/':{
                    target: 'http://*************:8075/',//
                },
                '/ia': {
                    // target: "http://************:8080", //服务
                    // target: 'http://*************:8451', //张
                    // target: 'http://*************:8074', //海航
                    // target: 'http://***********:8098', //锟锟
                    // target: 'http://***********1:8098', 
                    target: 'http://***********:10019', //测试
                    // target: 'http://127.0.0.1:8098', //本地

                    headers: { 'Accept-Encoding': 'identity' },
                },
            },
        },
    },
}
